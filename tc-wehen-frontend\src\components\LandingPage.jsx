import React, { useState } from 'react';
import './LandingPage.css';

const LandingPage = ({ onBookingClick }) => {
  const [currentPage, setCurrentPage] = useState('home');

  const navigate = (page) => {
    // Handle external redirects
    if (page === 'medenspiele') {
      window.open('https://htv.liga.nu/cgi-bin/WebObjects/nuLigaTENDE.woa/wa/clubMeetings?club=24972', '_blank');
      return;
    }
    
    if (page === 'mitglied-werden') {
      window.open('http://tc-wehen.de/wp-content/uploads/2025/04/Beitragsordnung_TCWehen_2025.pdf', '_blank');
      return;
    }
    
    if (page === 'satzung') {
      window.open('https://tc-wehen.com/wp-content/uploads/2021/03/Satzung_TC-Wehen_Maerz_2020.pdf', '_blank');
      return;
    }
    
    if (page === 'hand-spanndienst') {
      window.open('http://tc-wehen.de/wp-content/uploads/2024/04/TCW_HSD_Arbeitsstunden.pdf', '_blank');
      return;
    }
    
    if (page === 'mach-mit') {
      window.open('http://tc-wehen.de/wp-content/uploads/2024/01/Mithilfe_Mitglieder.pdf', '_blank');
      return;
    }

    if (page === 'buchung') {
      onBookingClick();
      return;
    }
    
    setCurrentPage(page);
  };

  const Header = () => (
    <header className="header">
      <div className="header-content">
        <div className="logo">
          <img src="/assets/Logo-TCW.PNG" alt="TC-Wehen Logo" className="logo-img" />
          <h1>TC-Wehen</h1>
        </div>
        <nav className="nav">
          <ul className="nav-list">
            <li><a href="#" onClick={() => navigate('home')} className={currentPage === 'home' ? 'active' : ''}>Home</a></li>
            <li><a href="#" onClick={() => navigate('news')} className={currentPage === 'news' ? 'active' : ''}>News</a></li>
            <li><a href="#" onClick={() => navigate('training')} className={currentPage === 'training' ? 'active' : ''}>Training</a></li>
            <li><a href="#" onClick={() => navigate('buchung')} className="booking-button">Platz buchen</a></li>
            <li className="dropdown">
              <a href="#" onClick={() => navigate('verein')} className={currentPage === 'verein' ? 'active' : ''}>Verein</a>
              <ul className="dropdown-menu">
                <li><a href="#" onClick={() => navigate('verein-anlage')}>Anlage</a></li>
                <li><a href="#" onClick={() => navigate('mitglied-werden')}>Mitglied werden</a></li>
                <li><a href="#" onClick={() => navigate('satzung')}>Satzung</a></li>
                <li><a href="#" onClick={() => navigate('hand-spanndienst')}>Hand- und Spanndienst</a></li>
                <li><a href="#" onClick={() => navigate('mach-mit')}>Mach Mit!</a></li>
              </ul>
            </li>
            <li><a href="#" onClick={() => navigate('medenspiele')} className={currentPage === 'medenspiele' ? 'active' : ''}>Medenspiele</a></li>
            <li><a href="#" onClick={() => navigate('kontakt')} className={currentPage === 'kontakt' ? 'active' : ''}>Kontakt</a></li>
          </ul>
        </nav>
      </div>
    </header>
  );

  const HomePage = () => (
    <div className="page home-page" style={{backgroundImage: "url('/assets/Plätze.PNG')"}}>
      <div className="welcome-message">
        <h2>Herzlich Willkommen beim TC-Wehen!</h2>
      </div>
    </div>
  );

  const NewsPage = () => (
    <div className="page news-page" style={{backgroundImage: "url('/assets/Terrassenansicht-Plätze.PNG')"}}>
      <div className="content">
        {/* Nur Header Navigation - Seite ist fast leer wie gewünscht */}
      </div>
    </div>
  );

  const TrainingPage = () => (
    <div className="page training-page" style={{backgroundImage: "url('/assets/Plätze3.PNG')"}}>
      <div className="content">
        <div className="training-content">
          <img src="/assets/Tennisschule-Prätorius.PNG" alt="Tennisschule Prätorius" className="training-logo" />
          <div className="trainer-placeholder">
            <div className="trainer-image-placeholder">
              [Platzhalter für Bild von Frank Prätorius]
            </div>
            <p className="training-message">Professionelles Tennistraining für alle Altersgruppen und Spielstärken!</p>
            <div className="contact-info">
              <p><strong>Kontakt:</strong></p>
              <p>Email: Beispiel.Frank@Prätorius.TC-Wehen</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const VereinPage = () => (
    <div className="page verein-page" style={{backgroundImage: "url('/assets/Plätze2.PNG')"}}>
      <div className="content">
        <div className="verein-content">
          <h2>Unser Verein</h2>
          <div className="vorstand-placeholder">
            [Platzhalter für Gruppenfoto des Vorstandes]
          </div>
          <div className="vorstand-liste">
            <h3>Vorstand:</h3>
            <ul>
              <li>1. Vorsitzender: [Name]</li>
              <li>2. Vorsitzender: [Name]</li>
              <li>Kassenwart: [Name]</li>
              <li>Schriftführer: [Name]</li>
              <li>Sportwart: [Name]</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const AnlagePage = () => (
    <div className="page anlage-page" style={{backgroundImage: "url('/assets/Obenansicht-Plätze.PNG')"}}>
      <div className="content">
        <div className="anlage-content">
          <h2>Unsere Anlage</h2>
          <div className="anlage-images">
            <img src="/assets/Plätze.PNG" alt="Tennisplätze" />
            <img src="/assets/Plätze2.PNG" alt="Tennisplätze 2" />
            <img src="/assets/Plätze3.PNG" alt="Tennisplätze 3" />
            <img src="/assets/Terrassenansicht-Plätze.PNG" alt="Terrassenansicht" />
            <img src="/assets/Obenansicht-Plätze.PNG" alt="Obenansicht" />
          </div>
          <p>Hier beim TC-Wehen haben wir 6 schöne und gepflegte Sandplätze und eine Ballwand. Sonne lässt sich bis zum Abend wunderschön auf unserer Terrasse bewundern! Kommt doch mal vorbei!</p>
          <div className="google-maps">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2515.123456789!2d8.123456!3d50.123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2sTC%20Wehen!5e0!3m2!1sde!2sde!4v1234567890"
              width="100%"
              height="300"
              style={{border: 0}}
              allowFullScreen=""
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="TC-Wehen Standort"
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  );

  const KontaktPage = () => (
    <div className="page kontakt-page" style={{backgroundImage: "url('/assets/Ball.PNG')"}}>
      <div className="content">
        <div className="kontakt-content">
          <h2>Kontakt</h2>
          <div className="kontakt-info">
            <p><strong>Telefon:</strong> [Platzhalter Telefonnummer]</p>
            <p><strong>Email:</strong> [Platzhalter Club Email]</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPage = () => {
    switch(currentPage) {
      case 'home':
        return <HomePage />;
      case 'news':
        return <NewsPage />;
      case 'training':
        return <TrainingPage />;
      case 'verein':
        return <VereinPage />;
      case 'verein-anlage':
        return <AnlagePage />;
      case 'kontakt':
        return <KontaktPage />;
      default:
        return <HomePage />;
    }
  };

  return (
    <div className="landing-page">
      <Header />
      <main className="main">
        {renderPage()}
      </main>
    </div>
  );
};

export default LandingPage;
