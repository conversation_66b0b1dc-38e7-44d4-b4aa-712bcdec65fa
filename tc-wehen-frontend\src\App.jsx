import { useState } from 'react'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import LandingPage from './components/LandingPage'
import Header from './components/Header'
import Login from './components/Login'
import Register from './components/Register'
import BookingCalendar from './components/BookingCalendar'

function AuthPage() {
  const [isLogin, setIsLogin] = useState(true)

  return (
    <div style={{
      minHeight: '100vh',
      background: 'var(--gradient-primary)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '2rem 1rem'
    }}>
      {isLogin ? (
        <Login onToggleMode={() => setIsLogin(false)} />
      ) : (
        <Register onToggleMode={() => setIsLogin(true)} />
      )}
    </div>
  )
}

function Dashboard() {
  return (
    <div style={{ minHeight: '100vh', background: 'var(--gray-50)' }}>
      <Header />
      <BookingCalendar />
    </div>
  )
}

function AppContent() {
  const { user, loading, showBooking, setShowBooking } = useAuth()

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'var(--gray-50)'
      }}>
        <div className="text-center">
          <div style={{
            width: '60px',
            height: '60px',
            background: 'var(--gradient-primary)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '1.5rem',
            margin: '0 auto 1rem',
            animation: 'pulse 2s infinite'
          }}>
            TC
          </div>
          <p style={{ color: 'var(--gray-600)' }}>Lade...</p>
        </div>
      </div>
    )
  }

  // Show booking page only if user is logged in and booking is requested
  if (showBooking && user) {
    return <Dashboard />
  }

  // Show login page only if booking is requested but user is not logged in
  if (showBooking && !user) {
    return <AuthPage />
  }

  // Show public landing page by default
  return <LandingPage onBookingClick={() => setShowBooking(true)} />
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}

export default App
