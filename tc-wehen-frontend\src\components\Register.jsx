import { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { Mail, Lock, User, Phone, Hash, Eye, EyeOff } from 'lucide-react'

const Register = ({ onToggleMode }) => {
  const { signUp } = useAuth()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: '',
    membershipNumber: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (formData.password !== formData.confirmPassword) {
      setError('Passwörter stimmen nicht überein')
      setLoading(false)
      return
    }

    if (formData.password.length < 6) {
      setError('Passwort muss mindestens 6 Zeichen lang sein')
      setLoading(false)
      return
    }

    try {
      const { error } = await signUp(
        formData.email,
        formData.password,
        formData.fullName,
        formData.phone,
        formData.membershipNumber
      )
      
      if (error) {
        setError(error.message)
      } else {
        setSuccess(true)
      }
    } catch (err) {
      setError('Ein Fehler ist aufgetreten')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  if (success) {
    return (
      <div className="card" style={{ maxWidth: '400px', margin: '0 auto' }}>
        <div className="text-center">
          <div style={{
            width: '60px',
            height: '60px',
            background: 'var(--gradient-primary)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '1.5rem',
            margin: '0 auto 1rem'
          }}>
            ✓
          </div>
          <h2 style={{ marginBottom: '1rem' }}>Registrierung erfolgreich!</h2>
          <p style={{ color: 'var(--gray-600)', marginBottom: '1.5rem' }}>
            Bitte überprüfen Sie Ihre E-Mail und bestätigen Sie Ihr Konto.
          </p>
          <button
            onClick={onToggleMode}
            className="btn btn-primary"
          >
            Zur Anmeldung
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="card" style={{ maxWidth: '400px', margin: '0 auto' }}>
      <div className="text-center mb-6">
        <div style={{
          width: '60px',
          height: '60px',
          background: 'var(--gradient-primary)',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold',
          fontSize: '1.5rem',
          margin: '0 auto 1rem'
        }}>
          TC
        </div>
        <h2 style={{ marginBottom: '0.5rem' }}>Konto erstellen</h2>
        <p style={{ color: 'var(--gray-600)' }}>
          Werden Sie Mitglied des TC Wehen
        </p>
      </div>

      {error && (
        <div style={{
          background: '#fef2f2',
          border: '1px solid #fecaca',
          color: '#dc2626',
          padding: '0.75rem',
          borderRadius: '6px',
          marginBottom: '1rem',
          fontSize: '0.875rem'
        }}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label className="form-label">
            <User size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
            Vollständiger Name *
          </label>
          <input
            type="text"
            name="fullName"
            value={formData.fullName}
            onChange={handleChange}
            className="form-input"
            placeholder="Max Mustermann"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label">
            <Mail size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
            E-Mail *
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="form-input"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="form-group">
            <label className="form-label">
              <Phone size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
              Telefon
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className="form-input"
              placeholder="+49 123 456789"
            />
          </div>

          <div className="form-group">
            <label className="form-label">
              <Hash size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
              Mitgliedsnummer
            </label>
            <input
              type="text"
              name="membershipNumber"
              value={formData.membershipNumber}
              onChange={handleChange}
              className="form-input"
              placeholder="12345"
            />
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">
            <Lock size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
            Passwort *
          </label>
          <div style={{ position: 'relative' }}>
            <input
              type={showPassword ? 'text' : 'password'}
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="form-input"
              placeholder="Mindestens 6 Zeichen"
              style={{ paddingRight: '3rem' }}
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              style={{
                position: 'absolute',
                right: '0.75rem',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                color: 'var(--gray-400)',
                cursor: 'pointer'
              }}
            >
              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">
            <Lock size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
            Passwort bestätigen *
          </label>
          <div style={{ position: 'relative' }}>
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              className="form-input"
              placeholder="Passwort wiederholen"
              style={{ paddingRight: '3rem' }}
              required
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              style={{
                position: 'absolute',
                right: '0.75rem',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                color: 'var(--gray-400)',
                cursor: 'pointer'
              }}
            >
              {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
          </div>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="btn btn-primary"
          style={{ width: '100%', marginBottom: '1rem' }}
        >
          {loading ? 'Registrierung läuft...' : 'Registrieren'}
        </button>
      </form>

      <div className="text-center">
        <p style={{ color: 'var(--gray-600)', marginBottom: '0.5rem' }}>
          Bereits ein Konto?
        </p>
        <button
          onClick={onToggleMode}
          className="btn btn-outline"
        >
          Jetzt anmelden
        </button>
      </div>
    </div>
  )
}

export default Register
