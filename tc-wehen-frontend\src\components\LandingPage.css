/* TC-<PERSON><PERSON> Page Styles */
:root {
  --primary-red: #c53030;
  --primary-orange: #dd6b20;
  --primary-white: #ffffff;
  --text-dark: #333333;
  --text-light: #666666;
  --shadow: rgba(0, 0, 0, 0.1);
  --logo-blue: #2b6cb0;
  --logo-green: #38a169;
  --light-bg: #f7fafc;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.landing-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--primary-white);
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-orange) 50%, var(--logo-blue) 100%);
  color: var(--primary-white);
  padding: 1rem 0;
  box-shadow: 0 2px 10px var(--shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: transform 0.3s ease-in-out;
}

.header-visible {
  transform: translateY(0);
}

.header-hidden {
  transform: translateY(-100%);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 2rem;
  gap: 3rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--primary-white);
  flex-shrink: 0;
}

.logo-img {
  height: 55px;
  width: 55px;
  border-radius: 50%;
  object-fit: contain;
  background: rgba(255, 255, 255, 0.1);
  padding: 3px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.logo h1 {
  font-size: 2.2rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: var(--primary-white);
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Navigation Styles */
.nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
  margin: 0;
  padding: 0;
}

.nav-list a {
  color: var(--primary-white);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-list a:hover,
.nav-list a.active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-arrow {
  font-size: 0.8rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.dropdown-open .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--primary-white);
  min-width: 200px;
  box-shadow: 0 5px 15px var(--shadow);
  border-radius: 5px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Desktop dropdown - only show on hover, not by default */
@media (min-width: 769px) {
  .dropdown-menu {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
  }

  .dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.dropdown-menu li {
  list-style: none;
}

.dropdown-menu a {
  color: var(--text-dark) !important;
  display: block;
  padding: 0.75rem 1rem;
  border-radius: 0;
  font-weight: normal;
}

.dropdown-menu a:hover {
  background-color: var(--primary-red);
  color: var(--primary-white) !important;
  transform: none;
}

/* Booking Button Styling */
.booking-button {
  background: linear-gradient(45deg, #ff6b35, #d32f2f) !important;
  font-weight: bold !important;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

.booking-button:hover {
  background: linear-gradient(45deg, #d32f2f, #ff6b35) !important;
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5) !important;
  transform: translateY(-3px) !important;
}

/* Main Content */
.main {
  flex: 1;
  padding-top: 80px; /* Account for fixed header */
}

.page {
  min-height: calc(100vh - 80px);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
}

.page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 1rem auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.97);
  border-radius: 15px;
  box-shadow: 0 10px 30px var(--shadow);
  backdrop-filter: blur(10px);
}

/* Home Page */
.home-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.welcome-message {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 3rem;
  border-radius: 15px;
  box-shadow: 0 15px 35px var(--shadow);
  backdrop-filter: blur(10px);
}

.welcome-message h2 {
  font-size: 3rem;
  color: var(--primary-red);
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px var(--shadow);
}

.welcome-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 0;
  font-style: italic;
}

/* News Section */
.news-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px var(--shadow);
  backdrop-filter: blur(10px);
}

.news-section h3 {
  color: var(--primary-red);
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
  text-shadow: 1px 1px 2px var(--shadow);
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.news-item {
  background: var(--white);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-orange);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.news-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.news-date {
  color: var(--logo-blue);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.news-item h4 {
  color: var(--primary-red);
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
  font-weight: 600;
}

.news-item p {
  color: var(--text-dark);
  line-height: 1.6;
  margin: 0;
}

/* Training Page */
.training-content {
  text-align: center;
}

.training-logo {
  max-width: 300px;
  height: auto;
  margin-bottom: 2rem;
}

/* Verein Page */
.verein-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.verein-content p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.8;
}

/* Anlage Page */
.anlage-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.anlage-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.anlage-images img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 5px 15px var(--shadow);
  transition: transform 0.3s ease;
}

.anlage-images img:hover {
  transform: scale(1.05);
}

/* Kontakt Page */
.kontakt-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.kontakt-info {
  margin-bottom: 2rem;
}

.kontakt-info h3 {
  color: var(--primary-red);
  margin-bottom: 1rem;
}

.kontakt-info p {
  margin-bottom: 0.5rem;
}

.maps-container {
  margin-top: 2rem;
}

/* Impressum Page */
.impressum-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.impressum-info h3 {
  color: var(--logo-blue);
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.impressum-info p {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

/* Datenschutz Page */
.datenschutz-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.datenschutz-info h3 {
  color: var(--logo-blue);
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.datenschutz-info h4 {
  color: var(--primary-orange);
  margin-top: 1.5rem;
  margin-bottom: 0.8rem;
  font-size: 1.1rem;
}

.datenschutz-info p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.datenschutz-info ul {
  margin: 1rem 0;
  padding-left: 2rem;
}

.datenschutz-info li {
  margin-bottom: 0.5rem;
}

/* Placeholder Page */
.placeholder-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.placeholder-info {
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.8;
}

.placeholder-info p {
  margin-bottom: 1.5rem;
}

/* Footer Styles */
.footer {
  background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-orange) 50%, var(--logo-blue) 100%);
  color: var(--primary-white);
  padding: 2rem 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.footer-links {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: var(--primary-white);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--light-bg);
  text-decoration: underline;
}

.footer-links span {
  color: rgba(255, 255, 255, 0.6);
}

.footer-text p {
  margin: 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Touch and Mobile Improvements */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Improved touch targets */
.nav-list a {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  /* Add padding-top to body to account for fixed header */
  body {
    padding-top: 80px;
  }

  .header-content {
    justify-content: space-between;
    padding: 0.5rem 1rem;
    gap: 1rem;
  }

  .logo {
    gap: 0.8rem;
  }

  .logo-img {
    height: 45px;
    width: 45px;
  }

  .logo h1 {
    font-size: 1.8rem;
  }

  /* Show hamburger menu on mobile */
  .mobile-menu-toggle {
    display: flex;
  }

  /* Hide navigation by default on mobile */
  .nav {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-orange) 50%, var(--logo-blue) 100%);
    transform: translateY(-100%);
    transition: transform 0.3s ease-in-out;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-height: calc(100vh - 80px);
    overflow-y: auto;
  }

  /* Show navigation when open */
  .nav-open {
    transform: translateY(0);
  }

  .nav-list {
    flex-direction: column;
    gap: 0;
    padding: 1rem 0;
    margin: 0;
    width: 100%;
  }

  .nav-list li {
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .nav-list li:last-child {
    border-bottom: none;
  }

  .nav-list a {
    display: block;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
    text-align: left;
    border-radius: 0;
    transition: background-color 0.3s ease;
  }

  .nav-list a:hover,
  .nav-list a.active {
    background-color: rgba(255, 255, 255, 0.2);
    transform: none;
  }

  .booking-button {
    background: rgba(255, 255, 255, 0.2) !important;
    font-weight: bold !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
  }

  .booking-button:hover {
    background: rgba(255, 255, 255, 0.3) !important;
  }



  /* Mobile dropdown - keep collapsed by default */
  .dropdown-menu {
    position: static;
    opacity: 0;
    visibility: hidden;
    max-height: 0;
    overflow: hidden;
    transform: none;
    box-shadow: none;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0;
    margin: 0;
    transition: all 0.3s ease;
  }

  /* Show dropdown on click (mobile) */
  .dropdown-open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    max-height: 200px;
  }

  .dropdown-menu a {
    padding-left: 3rem !important;
    font-size: 1rem !important;
    color: rgba(255, 255, 255, 0.9) !important;
  }

  /* Mobile Home Page */
  .home-content {
    padding: 1rem;
    gap: 1.5rem;
  }

  .welcome-message {
    padding: 2rem 1.5rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .news-section {
    padding: 1.5rem;
  }

  .news-section h3 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .news-item {
    padding: 1.2rem;
  }

  .news-item h4 {
    font-size: 1.1rem;
  }

  .content {
    padding: 1rem;
    margin: 0.5rem;
    border-radius: 15px;
  }

  .anlage-images {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Better mobile dropdown */
  .dropdown-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-top: 0.5rem;
  }

  .dropdown-menu a {
    color: var(--primary-white) !important;
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .dropdown-menu a:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  /* Mobile Footer */
  .footer-content {
    padding: 0 1rem;
  }

  .footer-links {
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .footer-text p {
    font-size: 0.8rem;
  }

  /* Mobile background optimization */
  .page {
    background-attachment: scroll;
    min-height: auto;
    padding: 2rem 0;
  }

  .page::before {
    background: rgba(0, 0, 0, 0.5);
  }
}

@media (max-width: 480px) {
  .logo h1 {
    font-size: 1.4rem;
  }

  .nav-list {
    font-size: 0.8rem;
    gap: 0.3rem;
  }

  .nav-list a {
    padding: 0.3rem 0.6rem;
  }

  .booking-button {
    font-size: 1rem !important;
    padding: 0.7rem !important;
  }

  .welcome-message {
    padding: 2rem 1rem;
  }

  .welcome-message h2 {
    font-size: 1.5rem;
  }
}
